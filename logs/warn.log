[35m[2025-08-11 11:27:46:114][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:27:47:177][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:27:47:536][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:27:47:541][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:27:47:543][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:27:51:694][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:53:716][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:55:754][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:57:760][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:57:761][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:27:59:793][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:28:01:812][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:28:01:813][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:28:02:046][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:28:03:265][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:28:03:265][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:28:03:789][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:28:03:835][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:24:638][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:30:24:974][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:30:25:361][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:30:25:369][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:30:25:371][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:30:29:503][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:31:540][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:33:566][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:35:584][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:35:585][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:30:37:605][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:39:621][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:39:621][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:30:39:692][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:30:40:380][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:30:40:380][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:30:40:686][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:30:41:651][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:19:997][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:31:20:272][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:31:20:581][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:31:20:585][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:31:20:587][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:31:24:710][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:26:734][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:28:763][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:30:782][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:30:783][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:31:32:814][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:34:838][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:34:839][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:31:34:904][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:31:35:579][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:31:35:580][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:31:35:889][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:31:36:854][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:34:58:572][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:34:59:751][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:34:59:751][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:35:00:041][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:37:04:241][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:37:05:501][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:37:05:501][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:37:05:816][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:37:30:678][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:37:31:993][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:37:31:993][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:37:32:307][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
