[35m[2025-08-11 11:27:46:114][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:27:47:177][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:27:47:536][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:27:47:541][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:27:47:543][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:27:51:694][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:53:716][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:55:754][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:57:760][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:27:57:761][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:27:59:793][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:28:01:812][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:28:01:813][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:28:02:046][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:28:03:265][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:28:03:265][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:28:03:789][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:28:03:835][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:24:638][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:30:24:974][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:30:25:361][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:30:25:369][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:30:25:371][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:30:29:503][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:31:540][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:33:566][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:35:584][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:35:585][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:30:37:605][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:39:621][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:30:39:621][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:30:39:692][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:30:40:380][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:30:40:380][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:30:40:686][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:30:41:651][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:19:997][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:31:20:272][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-08-11 11:31:20:581][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 11:31:20:585][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 11:31:20:587][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-08-11 11:31:24:710][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:26:734][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:28:763][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:30:782][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:30:783][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-08-11 11:31:32:814][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:34:838][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:31:34:839][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-08-11 11:31:34:904][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 11:31:35:579][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:31:35:580][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:31:35:889][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:31:36:854][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 11:34:58:572][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:34:59:751][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:34:59:751][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:35:00:041][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:37:04:241][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:37:05:501][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:37:05:501][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:37:05:816][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:37:30:678][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 11:37:31:993][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:37:31:993][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:37:32:307][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:38:55:304][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:38:55:305][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:38:55:635][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:40:54:194][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:40:54:194][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:40:54:501][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:44:14:681][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:44:14:681][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:44:14:973][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:47:22:308][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 11:47:22:309][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 11:47:22:946][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 11:47:22:956][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:09:40:718][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:09:40:718][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:09:41:306][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:09:41:316][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:11:37:272][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:11:37:276][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:11:41:396][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:43:419][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:45:438][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:47:460][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:47:460][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:11:49:489][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:51:513][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:51:513][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:11:51:560][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:11:52:214][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:11:52:214][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:11:52:846][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:11:52:855][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:11:53:542][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:11:59:331][m[WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:11:59:334][m[WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[35m[2025-08-11 13:11:59:583][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:01:417][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:01:418][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_131]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_131]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_131]
[35m[2025-08-11 13:12:01:418][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_131]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_131]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_131]
[35m[2025-08-11 13:12:06:424][m[WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:09:617][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:13:497][m[WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:19:893][m[WARN ][Thread-60][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889139892, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:12:20:553][m[WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:21:927][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:21:927][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_131]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_131]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_131]
[35m[2025-08-11 13:12:21:927][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_131]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_131]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_131]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_131]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_131]
[35m[2025-08-11 13:12:23:982][m[WARN ][Thread-60][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[35m[2025-08-11 13:12:29:743][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:12:29:748][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:12:33:853][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:35:876][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:37:894][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:39:917][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:39:917][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:12:41:938][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:43:953][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:43:953][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:12:44:007][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:12:44:733][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:12:44:733][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:12:45:439][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:12:45:451][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:12:45:977][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:12:52:005][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:02:036][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:20:075][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:29:036][m[WARN ][Thread-57][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889209036, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:13:35:453][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:13:36:199][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:13:36:207][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:13:40:315][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:42:341][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:44:370][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:46:394][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:46:395][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:13:48:422][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:50:442][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:50:443][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:13:50:495][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:13:51:255][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:13:51:255][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:13:51:603][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:13:52:468][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:13:54:054][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:13:58:503][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:14:08:507][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:14:26:541][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:00:572][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:19:931][m[WARN ][Thread-65][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889319931, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:15:26:608][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:15:27:315][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:15:27:320][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:15:31:431][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:33:444][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:35:478][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:37:485][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:37:486][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:15:39:510][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:41:533][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:41:534][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:15:41:592][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:15:42:263][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:15:42:263][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:15:42:578][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:15:43:541][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:44:609][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:15:49:578][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:15:59:116][m[WARN ][Thread-63][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889359116, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:15:59:614][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:11:974][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:17:12:586][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:17:12:591][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:17:16:710][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:18:723][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:20:728][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:22:749][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:22:749][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:17:24:778][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:26:804][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:26:804][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:17:26:857][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:17:27:535][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:17:27:535][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:17:27:888][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:17:28:816][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:30:013][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:17:34:828][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:17:44:831][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:02:879][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:23:765][m[WARN ][Thread-62][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889503765, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:18:30:639][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:18:31:282][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:18:31:287][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:18:35:416][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:37:439][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:39:457][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:41:466][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:41:466][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:18:43:487][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:45:507][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:18:45:507][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:18:45:557][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:18:46:316][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:18:46:316][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:18:46:634][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:18:47:520][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:34:579][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:19:35:206][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:19:35:210][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:19:39:324][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:41:351][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:43:381][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:45:400][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:45:401][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:19:47:428][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:49:457][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:19:49:457][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:19:49:501][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:19:50:240][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:19:50:240][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:19:50:549][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:19:51:482][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:08:556][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:20:09:167][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:20:09:171][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:20:13:288][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:15:317][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:17:330][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:19:351][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:19:351][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:20:21:363][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:23:375][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:23:375][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:20:23:429][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:20:24:099][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:20:24:099][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:20:24:412][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:20:25:397][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:20:53:949][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:20:54:604][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:20:54:608][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:20:58:728][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:00:754][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:02:784][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:04:801][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:04:801][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:21:06:839][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:08:850][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:08:851][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:21:08:916][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:21:09:688][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:21:09:689][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:21:10:042][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:21:10:878][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:12:372][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:21:16:908][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:26:947][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:21:35:208][m[WARN ][Thread-63][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889695208, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:22:10:115][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:22:10:755][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:22:10:759][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:22:14:876][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:16:890][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:18:920][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:20:945][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:20:945][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:22:22:970][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:24:997][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:24:997][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:22:25:055][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:22:25:775][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:22:25:775][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:22:26:225][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:22:27:021][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:28:438][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:22:33:043][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:22:43:073][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:23:01:118][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:23:35:149][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:24:41:181][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:03:143][m[WARN ][Thread-63][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754889903143, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:25:21:496][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:25:22:119][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:25:22:124][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:25:26:226][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:28:261][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:30:283][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:32:292][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:32:292][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:25:34:310][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:36:336][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:36:336][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:25:36:392][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:25:37:073][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:25:37:073][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:25:37:388][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:25:38:352][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:39:595][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:25:44:389][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:25:54:422][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:26:12:445][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:26:46:466][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:17:864][m[WARN ][Thread-62][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754890037864, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:27:27:437][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:27:28:049][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:27:28:053][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:27:32:173][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:34:192][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:36:212][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:38:234][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:38:234][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:27:40:241][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:42:254][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:42:254][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:27:42:302][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:27:42:981][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:27:42:982][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:27:43:300][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:27:44:270][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:27:45:472][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:27:50:287][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:28:00:315][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:28:18:350][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:28:52:380][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:29:58:406][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:18:988][m[WARN ][Thread-63][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754890218988, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:30:22:018][m[WARN ][DiscoveryClient-0][com.netflix.discovery.TimedSupervisorTask.run:85][][task supervisor shutting down, can't accept the task] 
[35m[2025-08-11 13:30:29:143][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:30:29:751][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:30:29:764][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:30:33:909][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:35:936][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:37:963][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:39:977][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:39:977][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:30:41:990][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:44:015][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:44:015][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:30:44:060][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:30:44:724][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:30:44:724][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:30:45:031][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:30:46:023][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:30:47:894][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:30:52:058][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:02:085][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:02:323][m[WARN ][Thread-63][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754890262323, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:31:10:165][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:31:10:829][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:31:10:834][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:31:14:945][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:16:966][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:18:970][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:20:991][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:20:992][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:31:23:020][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:25:048][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:25:048][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:31:25:102][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:31:25:862][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:31:25:862][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:31:26:266][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:31:27:065][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:29:308][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:31:33:146][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:31:43:212][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:01:259][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:10:779][m[WARN ][Thread-65][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754890330779, current=DOWN, previous=UP]] 
[35m[2025-08-11 13:32:13:809][m[WARN ][DiscoveryClient-0][com.netflix.discovery.TimedSupervisorTask.run:85][][task supervisor shutting down, can't accept the task] 
[35m[2025-08-11 13:32:21:610][m[WARN ][main][org.mybatis.spring.mapper.ClassPathMapperScanner.doScan:166][][No MyBatis mapper was found in '[com.holderzone.saas.store.member]' package. Please check your configuration.] 
[35m[2025-08-11 13:32:22:329][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-08-11 13:32:22:333][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-08-11 13:32:26:442][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:28:467][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:30:491][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:32:516][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:32:516][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties]] 
[35m[2025-08-11 13:32:34:543][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:36:567][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:36:567][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-store-member\config-cache\holder-saas-store-member+default+application.properties].] 
[35m[2025-08-11 13:32:36:622][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-08-11 13:32:37:450][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[35m[2025-08-11 13:32:37:450][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-08-11 13:32:37:765][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:32:38:594][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:41:016][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-08-11 13:32:44:694][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:32:54:721][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:33:12:750][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:33:46:780][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: holder-saas-store-member, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=holder-saas-store-member&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-08-11 13:34:02:232][m[WARN ][Thread-68][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1754890442232, current=DOWN, previous=UP]] 
