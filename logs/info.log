[35m[2025-08-11 11:27:45:365][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:27:47:182][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:28:03:155][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:196][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:320][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:28:03:789][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:28:03:834][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3a12f3e7] 
[35m[2025-08-11 11:29:29:498][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:23:975][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:24:976][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:30:40:308][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:329][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:424][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:30:40:687][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:30:40:701][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@560d6d2] 
[35m[2025-08-11 11:31:19:358][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:31:20:275][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:31:35:510][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:528][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:626][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:31:35:889][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:31:35:903][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@b529d7e] 
[35m[2025-08-11 11:34:57:881][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:34:59:682][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:34:59:701][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:34:59:793][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:35:00:042][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:35:00:055][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@27dc81be] 
[35m[2025-08-11 11:37:03:564][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:37:05:426][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:37:05:446][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:37:05:544][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:37:05:816][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:37:05:830][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@16e5344c] 
[35m[2025-08-11 11:37:29:981][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:37:31:919][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:37:31:939][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:37:32:039][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:37:32:307][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:37:32:321][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c83ed77] 
[35m[2025-08-11 11:38:53:330][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:38:55:213][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:38:55:237][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:38:55:363][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:38:55:635][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:38:55:650][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@286dfa20] 
[35m[2025-08-11 11:40:52:405][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:40:54:103][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:40:54:124][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:40:54:242][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:40:54:502][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:40:54:517][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@2579d8a] 
[35m[2025-08-11 11:44:13:091][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:44:14:609][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:44:14:627][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:44:14:725][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:44:14:973][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:44:14:987][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7e1a9173] 
