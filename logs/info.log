[35m[2025-08-11 11:27:45:365][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:27:47:182][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:28:03:155][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:196][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:320][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:28:03:789][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:28:03:834][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3a12f3e7] 
[35m[2025-08-11 11:29:29:498][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:23:975][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:24:976][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:30:40:308][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:329][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:424][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:30:40:687][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:30:40:701][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@560d6d2] 
[35m[2025-08-11 11:31:19:358][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:31:20:275][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:31:35:510][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:528][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:626][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:31:35:889][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:31:35:903][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@b529d7e] 
[35m[2025-08-11 11:34:57:881][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:34:59:682][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:34:59:701][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:34:59:793][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:35:00:042][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:35:00:055][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@27dc81be] 
[35m[2025-08-11 11:37:03:564][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:37:05:426][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:37:05:446][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:37:05:544][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:37:05:816][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:37:05:830][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@16e5344c] 
[35m[2025-08-11 11:37:29:981][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:37:31:919][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:37:31:939][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:37:32:039][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:37:32:307][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:37:32:321][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c83ed77] 
[35m[2025-08-11 11:38:53:330][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:38:55:213][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:38:55:237][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:38:55:363][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:38:55:635][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:38:55:650][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@286dfa20] 
[35m[2025-08-11 11:40:52:405][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:40:54:103][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:40:54:124][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:40:54:242][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:40:54:502][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:40:54:517][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@2579d8a] 
[35m[2025-08-11 11:44:13:091][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:44:14:609][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:44:14:627][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:44:14:725][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:44:14:973][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:44:14:987][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7e1a9173] 
[35m[2025-08-11 11:47:20:805][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:47:22:238][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:47:22:256][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:47:22:350][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:47:22:946][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:47:22:956][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:47:24:746][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 11:47:27:025][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 7.273 seconds (JVM running for 8.796)] 
[35m[2025-08-11 11:47:27:215][m[INFO ][RMI TCP Connection(2)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:09:34:390][m[INFO ][Thread-15][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:09:34:416][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:09:34:421][m[INFO ][Thread-26][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:09:39:207][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:09:40:649][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:09:40:667][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:09:40:760][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:09:41:308][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:09:41:316][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:09:42:503][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:09:44:227][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 6.029 seconds (JVM running for 7.71)] 
[35m[2025-08-11 13:09:44:707][m[INFO ][RMI TCP Connection(4)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:11:30:650][m[INFO ][Thread-14][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:11:30:657][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:11:30:663][m[INFO ][Thread-24][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:11:36:263][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:11:37:014][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:11:37:017][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:11:52:141][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:11:52:160][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:11:52:262][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:11:52:846][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:11:52:855][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:11:54:043][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:11:55:988][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:11:56:218][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:11:56:219][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:11:56:685][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:11:56:685][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:11:57:007][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:11:57:067][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:11:59:338][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:11:59:343][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:11:59:348][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889119345 with initial instances count: 0] 
[35m[2025-08-11 13:11:59:356][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889119356, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:11:59:358][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:11:59:442][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 24.202 seconds (JVM running for 25.806)] 
[35m[2025-08-11 13:11:59:831][m[INFO ][RMI TCP Connection(1)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:12:19:890][m[INFO ][Thread-43][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:12:19:893][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:12:19:899][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:12:19:901][m[INFO ][Thread-60][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:12:21:928][m[INFO ][Thread-60][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:12:23:989][m[INFO ][Thread-60][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:12:23:995][m[INFO ][Thread-60][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:12:28:748][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:12:29:483][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:12:29:485][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:12:44:654][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:12:44:675][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:12:44:779][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:12:45:440][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:12:45:452][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:12:46:826][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:12:48:968][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:12:49:071][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:12:49:071][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:12:49:480][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:12:49:480][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:12:49:770][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:12:49:828][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:12:49:829][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:12:49:829][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:12:49:829][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:12:49:829][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:12:49:830][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:12:49:830][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:12:50:444][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1056][][The response status is 200] 
[35m[2025-08-11 13:12:50:448][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:12:50:452][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:12:50:457][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889170455 with initial instances count: 62] 
[35m[2025-08-11 13:12:50:465][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889170465, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:12:50:467][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:12:50:579][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:12:50:606][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 22.873 seconds (JVM running for 24.736)] 
[35m[2025-08-11 13:12:51:082][m[INFO ][RMI TCP Connection(7)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:13:29:035][m[INFO ][Thread-43][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:13:29:036][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:13:29:041][m[INFO ][Thread-57][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:13:29:041][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:13:29:063][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:13:29:065][m[INFO ][Thread-57][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:13:29:086][m[INFO ][Thread-57][com.netflix.discovery.DiscoveryClient.unregister:924][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - deregister  status: 200] 
[35m[2025-08-11 13:13:29:094][m[INFO ][Thread-57][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:13:29:101][m[INFO ][Thread-57][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:13:34:764][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:13:35:759][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:13:35:762][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:13:51:172][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:13:51:195][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:13:51:306][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:13:51:603][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:13:51:619][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4552f905] 
[35m[2025-08-11 13:13:54:019][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-08-11 13:13:54:055][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:13:55:365][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:13:57:625][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:13:57:737][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:13:57:737][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:13:57:984][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:13:57:984][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:13:58:287][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:13:58:352][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:13:58:875][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1056][][The response status is 200] 
[35m[2025-08-11 13:13:58:880][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:13:58:885][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:13:58:890][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889238889 with initial instances count: 62] 
[35m[2025-08-11 13:13:58:903][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889238903, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:13:58:930][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:13:58:935][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:147][][Context refreshed] 
[35m[2025-08-11 13:13:59:000][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:150][][Found 1 custom documentation plugin(s)] 
[35m[2025-08-11 13:13:59:122][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:13:59:194][m[INFO ][main][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41][][Scanning for api listing references] 
[35m[2025-08-11 13:13:59:424][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_1] 
[35m[2025-08-11 13:13:59:426][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_2] 
[35m[2025-08-11 13:13:59:427][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_3] 
[35m[2025-08-11 13:13:59:429][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_4] 
[35m[2025-08-11 13:13:59:430][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_5] 
[35m[2025-08-11 13:13:59:431][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_6] 
[35m[2025-08-11 13:13:59:435][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_7] 
[35m[2025-08-11 13:13:59:436][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_8] 
[35m[2025-08-11 13:13:59:438][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_9] 
[35m[2025-08-11 13:13:59:439][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_10] 
[35m[2025-08-11 13:13:59:440][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_11] 
[35m[2025-08-11 13:13:59:441][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_1] 
[35m[2025-08-11 13:13:59:442][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_12] 
[35m[2025-08-11 13:13:59:444][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_13] 
[35m[2025-08-11 13:13:59:445][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_14] 
[35m[2025-08-11 13:13:59:446][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_15] 
[35m[2025-08-11 13:13:59:447][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_16] 
[35m[2025-08-11 13:13:59:448][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_2] 
[35m[2025-08-11 13:13:59:450][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_17] 
[35m[2025-08-11 13:13:59:451][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_18] 
[35m[2025-08-11 13:13:59:452][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_3] 
[35m[2025-08-11 13:13:59:454][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_4] 
[35m[2025-08-11 13:13:59:455][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_19] 
[35m[2025-08-11 13:13:59:589][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 25.926 seconds (JVM running for 27.445)] 
[35m[2025-08-11 13:14:00:149][m[INFO ][RMI TCP Connection(6)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:15:19:929][m[INFO ][Thread-51][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:15:19:932][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:15:19:941][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:15:19:961][m[INFO ][Thread-65][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:15:19:961][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:15:19:963][m[INFO ][Thread-65][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:15:19:994][m[INFO ][Thread-65][com.netflix.discovery.DiscoveryClient.unregister:924][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - deregister  status: 200] 
[35m[2025-08-11 13:15:20:000][m[INFO ][Thread-65][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:15:20:006][m[INFO ][Thread-65][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:15:25:665][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:15:26:999][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:15:27:001][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:15:42:190][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:15:42:210][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:15:42:308][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:15:42:578][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:15:42:593][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@15f11bfb] 
[35m[2025-08-11 13:15:44:582][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-08-11 13:15:44:609][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:15:45:672][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:15:47:476][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:15:47:578][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:15:47:579][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:15:47:812][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:15:47:812][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:15:48:078][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:15:48:137][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:15:48:138][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:15:48:625][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1056][][The response status is 200] 
[35m[2025-08-11 13:15:48:629][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:15:48:634][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:15:48:638][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889348636 with initial instances count: 62] 
[35m[2025-08-11 13:15:48:645][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889348645, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:15:48:647][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:15:48:651][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:147][][Context refreshed] 
[35m[2025-08-11 13:15:48:717][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:150][][Found 1 custom documentation plugin(s)] 
[35m[2025-08-11 13:15:48:792][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:15:48:868][m[INFO ][main][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41][][Scanning for api listing references] 
[35m[2025-08-11 13:15:49:084][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_1] 
[35m[2025-08-11 13:15:49:086][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_2] 
[35m[2025-08-11 13:15:49:087][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_3] 
[35m[2025-08-11 13:15:49:089][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_4] 
[35m[2025-08-11 13:15:49:091][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_5] 
[35m[2025-08-11 13:15:49:092][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_6] 
[35m[2025-08-11 13:15:49:096][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_7] 
[35m[2025-08-11 13:15:49:097][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_8] 
[35m[2025-08-11 13:15:49:099][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_9] 
[35m[2025-08-11 13:15:49:100][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_10] 
[35m[2025-08-11 13:15:49:101][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_11] 
[35m[2025-08-11 13:15:49:102][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_12] 
[35m[2025-08-11 13:15:49:104][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_1] 
[35m[2025-08-11 13:15:49:105][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_13] 
[35m[2025-08-11 13:15:49:106][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_14] 
[35m[2025-08-11 13:15:49:107][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_15] 
[35m[2025-08-11 13:15:49:109][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_16] 
[35m[2025-08-11 13:15:49:110][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_2] 
[35m[2025-08-11 13:15:49:112][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_17] 
[35m[2025-08-11 13:15:49:114][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_18] 
[35m[2025-08-11 13:15:49:115][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_3] 
[35m[2025-08-11 13:15:49:116][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_4] 
[35m[2025-08-11 13:15:49:118][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_19] 
[35m[2025-08-11 13:15:49:248][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 24.741 seconds (JVM running for 27.635)] 
[35m[2025-08-11 13:15:49:546][m[INFO ][RMI TCP Connection(4)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:15:59:114][m[INFO ][Thread-50][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:15:59:117][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:15:59:122][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:15:59:135][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:15:59:150][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:16:02:152][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:16:02:169][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.unregister:924][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - deregister  status: 200] 
[35m[2025-08-11 13:16:02:174][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:16:02:177][m[INFO ][Thread-63][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:16:12:099][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:16:42:746][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:17:11:322][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:17:12:275][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:17:12:278][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:17:27:462][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:17:27:482][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:17:27:581][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:17:27:889][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:17:27:902][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@625f5712] 
[35m[2025-08-11 13:17:29:983][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-08-11 13:17:30:014][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:17:31:130][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:17:33:153][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:17:33:273][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:17:33:273][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:17:33:528][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:17:33:528][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:17:33:817][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:17:33:875][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:17:33:876][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:17:34:379][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1056][][The response status is 200] 
[35m[2025-08-11 13:17:34:383][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:17:34:387][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:17:34:391][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889454390 with initial instances count: 62] 
[35m[2025-08-11 13:17:34:398][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889454398, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:17:34:400][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:17:34:403][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:147][][Context refreshed] 
[35m[2025-08-11 13:17:34:422][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:150][][Found 1 custom documentation plugin(s)] 
[35m[2025-08-11 13:17:34:581][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:17:34:620][m[INFO ][main][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41][][Scanning for api listing references] 
[35m[2025-08-11 13:17:34:742][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 24.433 seconds (JVM running for 26.333)] 
[35m[2025-08-11 13:17:34:999][m[INFO ][RMI TCP Connection(4)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:18:23:764][m[INFO ][Thread-49][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:18:23:766][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:18:23:772][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:18:23:786][m[INFO ][Thread-62][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:18:23:790][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:18:23:791][m[INFO ][Thread-62][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:18:23:806][m[INFO ][Thread-62][com.netflix.discovery.DiscoveryClient.unregister:924][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - deregister  status: 200] 
[35m[2025-08-11 13:18:23:811][m[INFO ][Thread-62][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:18:23:815][m[INFO ][Thread-62][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:18:29:960][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:18:30:961][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:18:30:965][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:18:46:237][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:18:46:259][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:18:46:364][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:18:46:634][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:18:46:648][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7921a37d] 
[35m[2025-08-11 13:19:33:897][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:19:34:886][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:19:34:888][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:19:50:168][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:19:50:187][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:19:50:284][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:19:50:549][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:19:50:563][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@59b853aa] 
[35m[2025-08-11 13:20:07:871][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:20:08:859][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:20:08:863][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:20:24:026][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:20:24:045][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:20:24:145][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:20:24:412][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:20:24:426][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@55b74e6b] 
[35m[2025-08-11 13:20:53:285][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 13:20:54:280][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:101][][App ID is set to holder-saas-store-member by app.id property from /META-INF/app.properties] 
[35m[2025-08-11 13:20:54:284][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 13:21:09:605][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 13:21:09:627][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 13:21:09:737][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 13:21:10:043][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:21:10:057][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@5f9ebd5a] 
[35m[2025-08-11 13:21:12:338][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-08-11 13:21:12:372][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 13:21:13:542][m[INFO ][main][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.mqProducer:64][][RocketMQ Producer Started, NamesrvAddr:test-holder-saas-rocketmq:9876, Group:memberProduce] 
[35m[2025-08-11 13:21:15:554][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-08-11 13:21:15:670][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:21:15:671][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-08-11 13:21:15:918][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-08-11 13:21:15:918][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-08-11 13:21:16:213][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-08-11 13:21:16:277][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-08-11 13:21:16:278][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-08-11 13:21:16:278][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-08-11 13:21:16:279][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-08-11 13:21:16:279][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-08-11 13:21:16:279][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-08-11 13:21:16:279][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-08-11 13:21:16:794][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1056][][The response status is 200] 
[35m[2025-08-11 13:21:16:798][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-08-11 13:21:16:803][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-08-11 13:21:16:808][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1754889676806 with initial instances count: 62] 
[35m[2025-08-11 13:21:16:818][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1754889676818, current=UP, previous=STARTING]] 
[35m[2025-08-11 13:21:16:821][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:21:16:825][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:147][][Context refreshed] 
[35m[2025-08-11 13:21:16:874][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:150][][Found 1 custom documentation plugin(s)] 
[35m[2025-08-11 13:21:16:990][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:21:17:051][m[INFO ][main][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41][][Scanning for api listing references] 
[35m[2025-08-11 13:21:17:179][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreMemberApplication in 24.999 seconds (JVM running for 26.529)] 
[35m[2025-08-11 13:21:17:712][m[INFO ][RMI TCP Connection(6)-**************][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:21:35:207][m[INFO ][Thread-50][com.holderzone.framework.rocketmq.config.RocketMqAutoConfiguration.lambda$mqProducer$0:58][][Producer shutdown] 
[35m[2025-08-11 13:21:35:208][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911: registering service...] 
[35m[2025-08-11 13:21:35:212][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:835][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - registration status: 204] 
[35m[2025-08-11 13:21:35:214][m[INFO ][NettyClientSelector_1][org.apache.rocketmq.logging.Slf4jLoggerFactory$Slf4jLogger.info:95][][closeChannel: close the connection to remote address[***************:9876] result: true] 
[35m[2025-08-11 13:21:35:231][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-08-11 13:21:38:241][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-08-11 13:21:38:257][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.unregister:924][][DiscoveryClient_HOLDER-SAAS-STORE-MEMBER/**************:8911 - deregister  status: 200] 
[35m[2025-08-11 13:21:38:262][m[INFO ][Thread-63][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
[35m[2025-08-11 13:21:38:265][m[INFO ][Thread-63][io.undertow.servlet.spec.ServletContextImpl.log:360][][Destroying Spring FrameworkServlet 'dispatcherServlet'] 
[35m[2025-08-11 13:21:47:824][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
