[35m[2025-08-11 11:27:45:365][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:27:47:182][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:28:03:155][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:196][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:28:03:320][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:28:03:789][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:28:03:834][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3a12f3e7] 
[35m[2025-08-11 11:29:29:498][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:23:975][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:30:24:976][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:30:40:308][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:329][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:30:40:424][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:30:40:687][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:30:40:701][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@560d6d2] 
[35m[2025-08-11 11:31:19:358][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:663][][The following profiles are active: dev] 
[35m[2025-08-11 11:31:20:275][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-08-11 11:31:35:510][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:528][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-08-11 11:31:35:626][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:360][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-08-11 11:31:35:889][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-08-11 11:31:35:903][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@b529d7e] 
