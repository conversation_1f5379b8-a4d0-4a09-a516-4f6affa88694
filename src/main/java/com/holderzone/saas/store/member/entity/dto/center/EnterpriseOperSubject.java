package com.holderzone.saas.store.member.entity.dto.center;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EnterpriseOperSubject implements Serializable {


    private String enterpriseGuid;

    @ApiModelProperty("餐饮云运营主体guid")
    private String multiMemberGuid;

    @ApiModelProperty("会员中台运营主体guid")
    private String centerOperSubjectGuid;

    @ApiModelProperty("会员中台企业guid")
    private String centerEnterpriseGuid;

    @ApiModelProperty("来源")
    private Integer source;
}
