package com.holderzone.saas.store.member.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.member.service.SendMessageService;
import com.holderzone.saas.store.member.service.rpc.MemberTransactionPrintService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageUtil
 * @date 2018/11/01 11:18
 * @description //TODO
 * @program holder-saas-store-order
 */
// @Component  // 临时禁用短信工具类
public class SendMessageUtil {

    private ExecutorService executorService = new ThreadPoolExecutor(5,20,
            5L, TimeUnit.SECONDS,new ArrayBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("message-pool-%d").build());
    private static final Logger logger = LoggerFactory.getLogger(SendMessageUtil.class);
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private MemberTransactionPrintService memberTransactionPrintService;

    public  void sendMessage(MessageDTO m1,String entGuid,Integer type){
        executorService.submit(() -> {
            try {
                 sendMessageService.sendMessage(m1,entGuid,type);
            } catch (Exception e) {
                logger.error("发送短信失败,msg={}",e.getMessage());
            }
        });
    }


    public void print(PrintStoredCashDTO st) {
        String jsonStr = UserContextUtils.getJsonStr();

        executorService.submit(() -> {
            try {
                UserContextUtils.put(jsonStr);
                memberTransactionPrintService.printMemberTransactionLog(st);
            } catch (Exception e) {
                logger.error("储值单打印失败,msg={}",e.getMessage());
            }
        });
    }


}
