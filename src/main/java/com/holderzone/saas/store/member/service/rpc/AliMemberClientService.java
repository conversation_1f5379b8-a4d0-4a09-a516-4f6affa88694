package com.holderzone.saas.store.member.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Component
@FeignClient(name = "HOLDER-SAAS-MEMBER-TERMINAL", fallbackFactory = AliMemberClientService.AliMemberClientServiceFallBack.class)
public interface AliMemberClientService {

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/hsmca/member/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail);

    /**
     * 在某门店查找会员及卡信息
     */
    @PostMapping(value = "/hsmca/member/getMemberInfoAndCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO);

    /**
     * 修改会员基本信息
     */
    @PutMapping(value = "/hsmca/member/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO);

    /**
     * 会员新增
     *
     * @param saveCMemberDTO
     * @return
     */
    @PostMapping(value = "/hsmca/member/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String add(@RequestBody RequestSaveCMemberDTO saveCMemberDTO);

    @Slf4j
    @Component
    class AliMemberClientServiceFallBack implements FallbackFactory<AliMemberClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public AliMemberClientService create(Throwable throwable) {
            return new AliMemberClientService() {
                @Override
                public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
                    log.error(HYSTRIX_PATTERN, "queryMemberByPhoneTail", phoneTail, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoAndCard", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCardReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("在某门店查找会员及卡信息失败");
                }

                @Override
                public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
                    log.error(HYSTRIX_PATTERN, "updateMemberInfo", "memberInfoGuid" + memberInfoGuid + JacksonUtils.writeValueAsString(updateMemberInfoDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("修改会员基本信息失败");
                }

                @Override
                public String add(RequestSaveCMemberDTO saveCMemberDTO) {
                    log.error(HYSTRIX_PATTERN, "add", JacksonUtils.writeValueAsString(saveCMemberDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
