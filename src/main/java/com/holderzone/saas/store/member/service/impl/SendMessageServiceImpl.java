package com.holderzone.saas.store.member.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.member.service.SendMessageService;
import com.holderzone.saas.store.member.service.rpc.EntServiceClient;
import com.holderzone.saas.store.member.service.rpc.MsgClientService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageServiceImpl
 * @date 2018/11/01 16:51
 * @description //TODO
 * @program holder-saas-store-member
 */
// @Service  // 临时禁用短信服务
@Slf4j
public class SendMessageServiceImpl implements SendMessageService{
    private static final Logger logger = LoggerFactory.getLogger(SendMessageServiceImpl.class);
   @Autowired
   private  EntServiceClient es;
   @Autowired
   private MsgClientService ms;
   @Override
    public void sendMessage(MessageDTO m1,String entGuid,Integer type) throws Exception {
        List<DeductShortMessageDTO> ds=new ArrayList<>();
        DeductShortMessageDTO d=new DeductShortMessageDTO();
        d.setDeductCount(1);
        d.setEnterpriseGuid(entGuid);
        ds.add(d);
        MessageConfigDTO messageInfo = es.getMessageInfo(entGuid);
        //会员充值
       if (messageInfo!=null&&type.equals(0)){
           messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
           if (messageInfo.getResidueCount()>=1&&messageInfo.getAfterCharge()==1){
                log.info("会员充值 send message before request: entry {} " + JacksonUtils.writeValueAsString(messageInfo));
               ms.sendMessage(m1);
                log.info("会员充值 send message success ! ");
               es.deductShortMessage(ds);
           }
       }
        //会员消费
        if (messageInfo!=null&&type.equals(1)){
            messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
            if (messageInfo.getResidueCount()>=1&&messageInfo.getAfterConsume()==1){
                ms.sendMessage(m1);
                es.deductShortMessage(ds);
            }
        }

        //会员退款
        if (messageInfo!=null&&type.equals(2)){
            messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
            if (messageInfo.getResidueCount()>=1 ){
                ms.sendMessage(m1);
                es.deductShortMessage(ds);
            }
        }
        //会员注册
        if (messageInfo!=null&&type.equals(3)){
            log.info("member register send msg ");
            messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
            if (messageInfo.getResidueCount()>=1){
                ms.sendMessage(m1);
                DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formatTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.systemDefault()));
               logger.info ("注册短信发送请求时间:"+formatTime);
                es.deductShortMessage(ds);
            }
        }

    }
}
