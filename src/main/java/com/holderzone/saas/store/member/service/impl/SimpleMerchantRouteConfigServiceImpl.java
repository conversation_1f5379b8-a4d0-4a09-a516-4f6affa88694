package com.holderzone.saas.store.member.service.impl;

import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.service.MerchantRouteConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

/**
 * 简化的商户路由配置服务实现
 * 当外部服务不可用时提供默认配置
 *
 * <AUTHOR>
 * @date 2025/8/11
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "merchantRouteConfigServiceImpl")
public class SimpleMerchantRouteConfigServiceImpl implements MerchantRouteConfigService {

    @Override
    public MemberServiceTypeEnum getMemberServiceType() {
        log.info("使用简化路由配置服务，返回默认会员服务类型");
        return MemberServiceTypeEnum.ALI_MEMBER;
    }
}
