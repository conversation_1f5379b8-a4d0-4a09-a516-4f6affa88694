package com.holderzone.saas.store.member.entity.constant.center;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
public class MemberCenterApiConstant {
    /**
     * 搜索会员列表-分页
     */
    public static final String GET_MEMBERS_BY_PHONE = "/member/getMembersByPhone";
    /**
     * 查询会员资料信息
     */
    public static final String GET_MEMBER_INFO = "/member/info";
    /**
     * 查询会员卡列表
     */
    public static final String GET_MEMBER_CARD = "/card/myList";
    /**
     * 修改会员信息
     */
    public static final String UPDATE_MEMBER_INFO = "/member/update";
    /**
     * 会员新增
     */
    public static final String ADD_MEMBER = "/member/add";
    /**
     * 会员卡充值-现金支付
     */
    public static final String CARD_RECHARGE = "/card/cash_recharge";
}
