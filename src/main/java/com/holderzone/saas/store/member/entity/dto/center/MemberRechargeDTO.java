package com.holderzone.saas.store.member.entity.dto.center;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
@Builder
public class MemberRechargeDTO {

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 会员持卡GUID
     */
    private String memberInfoCardGuid;

    /**
     * 充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 充值门店GUID
     */
    private String storeGuid;

    /**
     * 充值门店名称
     */
    private String storeName;
}
