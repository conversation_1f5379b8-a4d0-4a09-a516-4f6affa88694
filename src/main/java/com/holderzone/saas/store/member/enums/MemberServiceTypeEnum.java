package com.holderzone.saas.store.member.enums;

import lombok.Getter;

/**
 * 会员服务类型枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Getter
public enum MemberServiceTypeEnum {

    /**
     * 会员中台服务
     */
    MEMBER_CENTER("MEMBER_CENTER", "会员中台服务"),

    /**
     * 餐饮会员服务(原holder-alliance-member)
     */
    ALI_MEMBER("ALI_MEMBER", "餐饮会员服务");

    private final String code;
    private final String description;

    MemberServiceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MemberServiceTypeEnum getByCode(String code) {
        for (MemberServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        // 默认返回餐饮会员服务
        return ALI_MEMBER;
    }
}
