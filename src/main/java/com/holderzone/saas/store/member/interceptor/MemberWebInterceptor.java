package com.holderzone.saas.store.member.interceptor;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.member.entity.dto.center.ThreadLocalOperSubjectCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 会员服务Web拦截器
 * 用于处理企业上下文和ThreadLocal清理
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Slf4j
@Component
public class MemberWebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取企业GUID
        String enterpriseGuid = request.getHeader("enterpriseGuid");
        if (StringUtils.hasText(enterpriseGuid)) {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            log.info("enterpriseGuid:{} has been put into userContext", enterpriseGuid);
        }
        // 获取运营主体GUID
        String operSubjectGuid = request.getHeader("operSubjectGuid");
        if (StringUtils.hasText(operSubjectGuid)) {
            UserContext userContext = UserContextUtils.get();
            userContext.setOperSubjectGuid(operSubjectGuid);
            UserContextUtils.put(userContext);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 后置处理，可以在这里添加响应处理逻辑
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadLocalOperSubjectCache.remove();
        UserContextUtils.remove();
        EnterpriseIdentifier.remove();
    }

}
