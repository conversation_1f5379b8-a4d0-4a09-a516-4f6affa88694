package com.holderzone.saas.store.member.service.rpc;


import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.member.entity.dto.center.EnterpriseOperSubject;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = MultiMemberClient.Fallback.class)
public interface MultiMemberClient {

    @ApiOperation("查询门店属于什么运营主体")
    @GetMapping("/enterprise/member/info/{organizationGuid}")
    MultiMemberDTO findMemberInfoByOrganizationGuid(@PathVariable(value = "organizationGuid") String organizationGuid);


    /**
     * 查询运营主体信息
     */
    @GetMapping("/multi/member/get/{multiMemberGuid}")
    EnterpriseOperSubject get(@RequestParam("multiMemberGuid") String multiMemberGuid);

    @Slf4j
    @Component
    class Fallback implements FallbackFactory<MultiMemberClient> {
        @Override
        public MultiMemberClient create(Throwable throwable) {
            return new MultiMemberClient() {
                @Override
                public MultiMemberDTO findMemberInfoByOrganizationGuid(String organizationGuid) {
                    return null;
                }

                @Override
                public EnterpriseOperSubject get(String multiMemberGuid) {
                    log.error("查询运营主体信息失败", throwable);
                    return null;
                }
            };
        }
    }
}
