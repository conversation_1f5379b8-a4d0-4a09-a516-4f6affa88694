package com.holderzone.saas.store.member.entity.dto.center;

import com.holderzone.framework.util.JacksonUtils;

import java.util.Optional;


/**
 * 会员中心线程缓存
 */
public final class ThreadLocalOperSubjectCache {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static void put(String str) {
        THREAD_LOCAL.set(str);
    }

    public static void putSource(Integer source) {
        EnterpriseOperSubject enterpriseOperSubject = getEnterpriseOperSubject();
        enterpriseOperSubject.setSource(source);
        put(JacksonUtils.writeValueAsString(enterpriseOperSubject));
    }

    public static String get() {
        return THREAD_LOCAL.get();
    }

    public static EnterpriseOperSubject getEnterpriseOperSubject() {
        String json = THREAD_LOCAL.get();
        return Optional.ofNullable(JacksonUtils.toObject(EnterpriseOperSubject.class, json)).orElse(new EnterpriseOperSubject());
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }

    public static String getEnterpriseGuid() {
        String result = THREAD_LOCAL.get();
        EnterpriseOperSubject enterpriseOperSubject = JacksonUtils.toObject(EnterpriseOperSubject.class, result);
        if (null != enterpriseOperSubject) {
            return enterpriseOperSubject.getEnterpriseGuid();
        }
        return null;
    }

    public static String getOperSubjectGuid() {
        String result = THREAD_LOCAL.get();
        EnterpriseOperSubject enterpriseOperSubject = JacksonUtils.toObject(EnterpriseOperSubject.class, result);
        if (null != enterpriseOperSubject) {
            return enterpriseOperSubject.getMultiMemberGuid();
        }
        return null;
    }

    public static String getCenterOperSubjectGuid() {
        String result = THREAD_LOCAL.get();
        EnterpriseOperSubject enterpriseOperSubject = JacksonUtils.toObject(EnterpriseOperSubject.class, result);
        if (null != enterpriseOperSubject) {
            return enterpriseOperSubject.getCenterOperSubjectGuid();
        }
        return null;
    }

    public static String getCenterEnterpriseGuid() {
        String result = THREAD_LOCAL.get();
        EnterpriseOperSubject enterpriseOperSubject = JacksonUtils.toObject(EnterpriseOperSubject.class, result);
        if (null != enterpriseOperSubject) {
            return enterpriseOperSubject.getCenterEnterpriseGuid();
        }
        return null;
    }

    public static Integer getSource() {
        String result = THREAD_LOCAL.get();
        EnterpriseOperSubject enterpriseOperSubject = JacksonUtils.toObject(EnterpriseOperSubject.class, result);
        if (null != enterpriseOperSubject) {
            return enterpriseOperSubject.getSource();
        }
        return null;
    }

}
