package com.holderzone.saas.store.member.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SwaggerConfig
 * @date 2018-08-06 11:26:18
 * @description
 * @program holder-saas-store-member
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public Docket testApi(){
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .enable(true)
                .groupName("member")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.holderzone.saas.store.member.controller"))
                .paths(PathSelectors.any())
                .build();
        return docket;
    }

    private ApiInfo apiInfo(){
        ApiInfo apiInfo = new ApiInfoBuilder()
                .title("saas平台会员中心")
                .description("saas平台会员中心")
                .version("1.0.0")
                .build();

        return apiInfo;
    }
}
