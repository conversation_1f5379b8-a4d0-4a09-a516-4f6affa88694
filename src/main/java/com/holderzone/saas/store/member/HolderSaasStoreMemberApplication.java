package com.holderzone.saas.store.member;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.member.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableSwagger2
//@EnableApolloConfig
public class HolderSaasStoreMemberApplication {


    public static void main(String[] args) {


        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreMemberApplication.class, args);

        /**
         * 设置Spring容器上下文
         */
        SpringContextUtils.getInstance().setCfgContext(app);


    }

}
