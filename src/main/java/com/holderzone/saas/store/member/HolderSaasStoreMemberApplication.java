package com.holderzone.saas.store.member;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.member.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        MybatisAutoConfiguration.class,
        RedisAutoConfiguration.class,
        EurekaClientAutoConfiguration.class,
        FeignAutoConfiguration.class,
        MetricsAutoConfiguration.class,
        ManagementContextAutoConfiguration.class
})
// @EnableDiscoveryClient  // 临时禁用服务发现
// @EnableFeignClients  // 临时禁用Feign客户端
// @EnableSwagger2  // 临时禁用Swagger
//@EnableApolloConfig
public class HolderSaasStoreMemberApplication {


    public static void main(String[] args) {


        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreMemberApplication.class, args);

        /**
         * 设置Spring容器上下文
         */
        SpringContextUtils.getInstance().setCfgContext(app);


    }

}
