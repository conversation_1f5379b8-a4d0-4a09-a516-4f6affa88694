
package com.holderzone.saas.store.member.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

@Slf4j
public class SignUtils {

    /**
     * 生成签名
     *
     * @param appId     应用ID
     * @param secretKey 密钥
     * @param timestamp 时间戳
     * @return 签名
     */
    public static String generateSignature(String appId,
                                           String secretKey,
                                           String timestamp) {
        try {
            // 构造签名字符串，格式：appId + timestamp
            String signStr = appId + timestamp;

            // 使用密钥加盐
            signStr += secretKey;

            // 使用SHA-256进行哈希
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(signStr.getBytes(StandardCharsets.UTF_8));

            // Base64编码
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("生成签名时发生错误", e);
            return "";
        }
    }
}
