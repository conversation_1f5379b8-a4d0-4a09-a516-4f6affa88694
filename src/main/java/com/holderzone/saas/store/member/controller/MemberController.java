package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.member.service.MemberService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by zhaohongyang on 2018/8/21.
 */
@RestController
@RequestMapping("/member")
@RequiredArgsConstructor
public class MemberController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MemberController.class);

    private final MemberService memberService;

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail) {
        logger.info("根据手机号号查询会员，参数：{}", phoneTail);
        return memberService.queryMemberByPhoneTail(phoneTail);
    }

    @ApiOperation("在某门店查找会员及卡信息")
    @PostMapping(value = "/getMemberInfoAndCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        logger.info("在某门店查找会员及卡信息，参数：{}", queryStoreAndMemberAndCardReqDTO);
        return memberService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
    }

    @ApiOperation("修改会员基本信息")
    @PostMapping(value = "/updateMemberInfo/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid, @RequestBody RequestUpdateMemberDTO updateMemberInfoDTO) {
        logger.info("修改会员基本信息，参数：{}", updateMemberInfoDTO);
        memberService.updateMemberInfo(memberInfoGuid, updateMemberInfoDTO);
    }

    @ApiOperation("会员新增")
    @PostMapping(value = "/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String add(@RequestBody RequestSaveCMemberDTO saveCMemberDTO) {
        logger.info("会员新增，参数：{}", saveCMemberDTO);
        return memberService.add(saveCMemberDTO);
    }

    @ApiOperation(value = "会员充值接口", notes = "会员充值接口")
    @PostMapping(value = "/recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public HsmAggPayRespDTO recharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("会员充值接口入参：{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO));
        }
        return memberService.recharge(hsmRechargeReqDTO);
    }

}
