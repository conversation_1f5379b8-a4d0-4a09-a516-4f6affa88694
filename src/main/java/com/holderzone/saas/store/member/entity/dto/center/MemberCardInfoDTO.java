package com.holderzone.saas.store.member.entity.dto.center;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员卡信息DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberCardInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员持卡GUID，hsa_member_info_card guid
     */
    private String ownGuid;

    /**
     * 会员卡GUID
     */
    private String cardGuid;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 会员卡类型 0 实体卡 1电子卡
     */
    private Integer cardType;

    /**
     * 卡颜色
     */
    private String cardColor;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 卡面值金额
     */
    private BigDecimal cardValueMoney;

    /**
     * 卡状态
     */
    private Integer cardStatus;

    /**
     * 卡状态
     */
    private Integer cardState;

    /**
     * 实体卡状态
     */
    private Integer physicalCardState;

    /**
     * 开卡日期
     */
    private String gmtCreate;

    /**
     * 卡有效期
     */
    private Integer cardValidity;

    /**
     * 卡有效期时间
     */
    private String cardValidityTime;

    /**
     * 卡有效期日期
     */
    private String cardValidityDate;

    /**
     * 有效期单位
     */
    private String validityUnit;

    /**
     * 账户余额
     */
    private BigDecimal accountMoney;

    /**
     * 开卡时间类型
     */
    private Integer openCardTimeType;

    /**
     * 默认卡
     */
    private Integer defaultCard;

    /**
     * 会员持卡GUID
     */
    private String memberInfoCardGuid;

    /**
     * 自助类型
     */
    private Integer selfType;

    /**
     * 自助支付金额
     */
    private BigDecimal selfPaymentMoney;

    /**
     * 自助充值金额
     */
    private BigDecimal selfRechargeMoney;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 电子卡开通方式
     */
    private Integer electronicOpenWay;

    /**
     * 应付金额
     */
    private BigDecimal shouldPayMoney;

    /**
     * 优惠金额
     */
    private BigDecimal economyMoney;

    /**
     * 绑定门店数量
     */
    private Integer bindingStoreCount;

    /**
     * 卡使用说明
     */
    private String cardEmployExplain;

    /**
     * 有效时间段
     */
    private String periodOfValidity;

    /**
     * 会员卡卡号
     */
    private String electronicCardNum;

    /**
     * 门店数量
     */
    private Integer storeNum;

    /**
     * 适用所有门店
     */
    private Integer applicableAllStore;

    /**
     * 是否支持电子卡
     */
    private Integer isSupportElectronicCard;

    /**
     * 是否需要密码
     */
    private Integer isPassword;

    /**
     * 是否支持超额
     */
    private Integer isExcess;

    /**
     * 超额类型 0 次数 1 金额
     */
    private Integer excessType;

    /**
     * 超额金额
     */
    private BigDecimal excessAmount;

    /**
     * 超额次数
     */
    private Integer excessTimes;

    /**
     * 是否适用当前门店
     */
    private Boolean isUsableStore;

    /**
     * 是否预存
     */
    private Integer isPreStored;

    /**
     * 小程序充值
     */
    private Integer appletRecharge;

    /**
     * 充值提示
     */
    private String rechargeTips;

    /**
     * 等级权益列表
     */
    private Object gradeEquitiesVOList;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体或联盟GUID
     */
    private String operSubjectGuid;

    /**
     * 80, "餐饮云-微信小程序"；81, "餐饮云-支付宝小程序"；82, "餐饮云-H5"；83, "餐饮云-一体机"；84, "餐饮云-POS"；85, "餐饮云-PAD
     */
    private Integer source;
}
