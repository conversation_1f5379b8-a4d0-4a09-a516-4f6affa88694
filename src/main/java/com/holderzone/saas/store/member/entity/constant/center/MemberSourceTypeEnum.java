package com.holderzone.saas.store.member.entity.constant.center;

import lombok.Getter;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Getter
public enum MemberSourceTypeEnum {

    /**
     * 餐饮云-微信小程序
     */
    WECHAT_MINI_PROGRAM(80, "餐饮云-微信小程序", 21),

    /**
     * 餐饮云-支付宝小程序
     */
    ALIPAY_MINI_PROGRAM(81, "餐饮云-支付宝小程序", 55),

    /**
     * 餐饮云-H5
     */
    H5(82, "餐饮云-H5", 24),

    /**
     * 餐饮云-一体机
     */
    ALL_IN_ONE_MACHINE(83, "餐饮云-一体机", 2),

    /**
     * 餐饮云-POS
     */
    POS(84, "餐饮云-POS", 1),

    /**
     * 餐饮云-PAD
     */
    PAD(85, "餐饮云-PAD", 5);

    /**
     * 源类型代码
     */
    private final Integer code;

    /**
     * 源类型描述
     */
    private final String description;

    /**
     * 门店会员来源代码（用于适配门店会员系统）
     */
    private final Integer storeMemberSourceCode;

    MemberSourceTypeEnum(Integer code, String description, Integer storeMemberSourceCode) {
        this.code = code;
        this.description = description;
        this.storeMemberSourceCode = storeMemberSourceCode;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 源类型代码
     * @return 对应的枚举值，如果未找到返回null
     */
    public static MemberSourceTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberSourceTypeEnum sourceType : values()) {
            if (sourceType.getCode().equals(code)) {
                return sourceType;
            }
        }
        return null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 源类型代码
     * @return 对应的描述，如果未找到返回null
     */
    public static String getDescriptionByCode(Integer code) {
        MemberSourceTypeEnum sourceType = getByCode(code);
        return sourceType != null ? sourceType.getDescription() : null;
    }

    /**
     * 根据门店会员来源代码获取枚举
     *
     * @param storeMemberSourceCode 门店会员来源代码
     * @return 对应的枚举值，如果未找到返回null
     */
    public static MemberSourceTypeEnum getByStoreMemberSourceCode(Integer storeMemberSourceCode) {
        if (storeMemberSourceCode == null) {
            return null;
        }
        for (MemberSourceTypeEnum sourceType : values()) {
            if (storeMemberSourceCode.equals(sourceType.getStoreMemberSourceCode())) {
                return sourceType;
            }
        }
        return null;
    }

    /**
     * 根据门店会员来源代码获取描述
     *
     * @param storeMemberSourceCode 门店会员来源代码
     * @return 对应的描述，如果未找到返回null
     */
    public static String getDescriptionByStoreMemberSourceCode(Integer storeMemberSourceCode) {
        MemberSourceTypeEnum sourceType = getByStoreMemberSourceCode(storeMemberSourceCode);
        return sourceType != null ? sourceType.getDescription() : null;
    }

    /**
     * 将会员中台源类型代码转换为门店会员来源代码
     *
     * @param memberCenterCode 会员中台源类型代码
     * @return 对应的门店会员来源代码，如果未找到返回null
     */
    public static Integer convertToStoreMemberSourceCode(Integer memberCenterCode) {
        MemberSourceTypeEnum sourceType = getByCode(memberCenterCode);
        return sourceType != null ? sourceType.getStoreMemberSourceCode() : null;
    }

    /**
     * 将门店会员来源代码转换为会员中台源类型代码
     *
     * @param storeMemberSourceCode 门店会员来源代码
     * @return 对应的会员中台源类型代码，如果未找到返回null
     */
    public static Integer convertToMemberCenterCode(Integer storeMemberSourceCode) {
        MemberSourceTypeEnum sourceType = getByStoreMemberSourceCode(storeMemberSourceCode);
        return sourceType != null ? sourceType.getCode() : null;
    }
}
