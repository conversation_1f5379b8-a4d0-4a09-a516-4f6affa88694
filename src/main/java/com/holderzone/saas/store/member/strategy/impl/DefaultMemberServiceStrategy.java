package com.holderzone.saas.store.member.strategy.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 默认会员服务策略实现
 * 用于在外部服务不可用时提供基本功能
 *
 * <AUTHOR>
 * @date 2025/8/11
 */
@Slf4j
@Component
public class DefaultMemberServiceStrategy implements MemberServiceStrategy {

    @Override
    public MemberServiceTypeEnum getServiceType() {
        return MemberServiceTypeEnum.ALI_MEMBER; // 使用默认类型
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        log.warn("使用默认会员服务策略，外部服务不可用，返回空列表");
        return Collections.emptyList();
    }

    @Override
    public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        log.warn("使用默认会员服务策略，外部服务不可用，返回空对象");
        return new ResponseMemberAndCardInfoDTO();
    }

    @Override
    public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
        log.warn("使用默认会员服务策略，外部服务不可用，更新操作被忽略");
    }

    @Override
    public String add(RequestSaveCMemberDTO saveCMemberDTO) {
        log.warn("使用默认会员服务策略，外部服务不可用，返回模拟ID");
        return "default-member-id-" + System.currentTimeMillis();
    }

    @Override
    public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        log.warn("使用默认会员服务策略，外部服务不可用，充值操作失败");
        HsmAggPayRespDTO response = new HsmAggPayRespDTO();
        response.setResult(false);
//        response.setMessage("服务暂时不可用");
        return response;
    }
}
