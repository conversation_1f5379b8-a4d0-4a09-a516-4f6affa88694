package com.holderzone.saas.store.member.entity.dto.center;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员充值响应DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
public class MemberRechargeResponseDTO {

    /**
     * 充值订单号
     */
    private String orderNumber;

    /**
     * 会员充值消费GUID
     */
    private String memberConsumptionGuid;

    /**
     * 会员来源门店名称
     */
    private String storeName;

    /**
     * 充值金额
     */
    private BigDecimal recharge;

    /**
     * 到账金额
     */
    private BigDecimal arrival;

    /**
     * 充值方式，0:现金支付；1:聚合支付
     */
    private Integer payWay;

    /**
     * 充值方式名称
     */
    private String payName;

    /**
     * 卡号
     */
    private String cardNum;

    /**
     * 当前金额
     */
    private BigDecimal currentCash;

    /**
     * 充值时间
     */
    private String rechargeTime;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 赠送总积分值
     */
    private Integer giftTotalIntegralValue;

    /**
     * 赠送总成长值
     */
    private Integer giftTotalGrowthValue;

    /**
     * 赠送卡名称列表
     */
    private List<String> giftTotalCardNameList;

    /**
     * 赠送生效总金额
     */
    private BigDecimal giftEffectuateTotalAmount;

    /**
     * 赠送固定总金额
     */
    private BigDecimal giftFixedTotalAmount;
}
