package com.holderzone.saas.store.member.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.member.entity.dto.center.ThreadLocalOperSubjectCache;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.factory.MemberServiceStrategyFactory;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.MerchantRouteConfigService;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 路由会员服务实现类
 * 根据商户配置路由到不同的会员服务
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoutedMemberServiceImpl implements MemberService {

    private final MerchantRouteConfigService merchantRouteConfigService;

    private final MemberServiceStrategyFactory memberServiceStrategyFactory;

    /**
     * 获取当前请求对应的会员服务策略
     *
     * @return 会员服务策略
     */
    private MemberServiceStrategy getCurrentStrategy() {

        MemberServiceTypeEnum serviceType = merchantRouteConfigService.getMemberServiceType();

        return memberServiceStrategyFactory.getStrategy(serviceType);
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        return getCurrentStrategy().queryMemberByPhoneTail(phoneTail);
    }

    @Override
    public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        return getCurrentStrategy().getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
    }

    @Override
    public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
        getCurrentStrategy().updateMemberInfo(memberInfoGuid, updateMemberInfoDTO);
    }

    @Override
    public String add(RequestSaveCMemberDTO saveCMemberDTO) {
        return getCurrentStrategy().add(saveCMemberDTO);
    }

    @Override
    public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        return getCurrentStrategy().recharge(hsmRechargeReqDTO);
    }
}
