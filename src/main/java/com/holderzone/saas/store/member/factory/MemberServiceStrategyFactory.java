package com.holderzone.saas.store.member.factory;

import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会员服务策略工厂
 * 
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Component  // 恢复策略工厂
public class MemberServiceStrategyFactory {
    
    @Autowired(required = false)  // 允许为空，避免启动失败
    private List<MemberServiceStrategy> memberServiceStrategies;

    private final Map<MemberServiceTypeEnum, MemberServiceStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (memberServiceStrategies != null && !memberServiceStrategies.isEmpty()) {
            for (MemberServiceStrategy strategy : memberServiceStrategies) {
                strategyMap.put(strategy.getServiceType(), strategy);
                log.info("注册会员服务策略: {}", strategy.getServiceType().getDescription());
            }
        } else {
            log.warn("没有找到可用的会员服务策略实现");
        }
    }
    
    /**
     * 根据服务类型获取策略
     * 
     * @param serviceType 服务类型
     * @return 会员服务策略
     */
    public MemberServiceStrategy getStrategy(MemberServiceTypeEnum serviceType) {
        MemberServiceStrategy strategy = strategyMap.get(serviceType);
        if (strategy == null) {
            log.warn("未找到对应的会员服务策略: {}，使用默认餐饮会员服务", serviceType);
            strategy = strategyMap.get(MemberServiceTypeEnum.ALI_MEMBER);
        }
        return strategy;
    }
}
