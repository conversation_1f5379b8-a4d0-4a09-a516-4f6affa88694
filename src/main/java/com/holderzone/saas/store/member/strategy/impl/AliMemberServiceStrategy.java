package com.holderzone.saas.store.member.strategy.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.service.HsmMemberService;
import com.holderzone.saas.store.member.service.rpc.AliMemberClientService;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 餐饮会员服务策略实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
// @Component  // 临时禁用策略实现
@RequiredArgsConstructor
public class AliMemberServiceStrategy implements MemberServiceStrategy {

    private final AliMemberClientService aliMemberClientService;

    private final HsmMemberService hsmMemberService;

    @Override
    public MemberServiceTypeEnum getServiceType() {
        return MemberServiceTypeEnum.ALI_MEMBER;
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        log.info("使用餐饮会员服务根据手机号后四位查询会员: {}", phoneTail);
        return aliMemberClientService.queryMemberByPhoneTail(phoneTail);
    }

    @Override
    public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        log.info("使用餐饮会员服务在某门店查找会员及卡信息: {}", queryStoreAndMemberAndCardReqDTO);
        return aliMemberClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
    }

    @Override
    public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
        log.info("使用餐饮会员服务修改会员基本信息: {}", updateMemberInfoDTO);
        aliMemberClientService.updateMemberInfo(memberInfoGuid, updateMemberInfoDTO);
    }

    @Override
    public String add(RequestSaveCMemberDTO saveCMemberDTO) {
        log.info("使用餐饮会员服务新增会员: {}", saveCMemberDTO);
        return aliMemberClientService.add(saveCMemberDTO);
    }

    @Override
    public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        log.info("使用餐饮会员服务会员充值: {}", hsmRechargeReqDTO);
        return hsmMemberService.recharge(hsmRechargeReqDTO);
    }
}
