package com.holderzone.saas.store.member.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.member.entity.dto.center.EnterpriseOperSubject;
import com.holderzone.saas.store.member.entity.dto.center.ThreadLocalOperSubjectCache;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.service.MerchantRouteConfigService;
import com.holderzone.saas.store.member.service.rpc.MultiMemberClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 商户路由配置服务实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
// @Service  // 临时禁用路由配置服务
@RequiredArgsConstructor
public class MerchantRouteConfigServiceImpl implements MerchantRouteConfigService {

    private static final String MEMBER_SERVICE_TYPE_KEY_PREFIX = "member:service:type:";

    private final StringRedisTemplate stringRedisTemplate;

    private final MultiMemberClient multiMemberClient;

    @Override
    public MemberServiceTypeEnum getMemberServiceType() {
        String operSubjectGuid = UserContextUtils.get().getOperSubjectGuid();
        if (StringUtils.isBlank(operSubjectGuid)) {
            log.warn("运营主体GUID为空，使用默认餐饮会员服务");
            return MemberServiceTypeEnum.ALI_MEMBER;
        }
        EnterpriseOperSubject multiMemberDTO;
        String entKey = MEMBER_SERVICE_TYPE_KEY_PREFIX + operSubjectGuid;
        String multiMemberStr = stringRedisTemplate.opsForValue().get(entKey);
        //缓存中没有值
        if (StringUtils.isBlank(multiMemberStr)) {
            //调用云平台查询配置信息
            log.info("查询运营主体guid:operSubjectGuid={}", operSubjectGuid);
            multiMemberDTO = multiMemberClient.get(operSubjectGuid);
            log.info("查询运营主体guid:multiMemberDTO={}", multiMemberDTO);
            if (Objects.isNull(multiMemberDTO)) {
                log.info("未找到企业配置，使用默认餐饮会员服务: operSubjectGuid={}", operSubjectGuid);
                return MemberServiceTypeEnum.ALI_MEMBER;
            }
            stringRedisTemplate.opsForValue().set(entKey, JacksonUtils.writeValueAsString(multiMemberDTO));
        }
        multiMemberDTO = JacksonUtils.toObject(EnterpriseOperSubject.class, multiMemberStr);
        ThreadLocalOperSubjectCache.put(JacksonUtils.writeValueAsString(multiMemberDTO));

        return StringUtils.isBlank(multiMemberDTO.getCenterOperSubjectGuid())
                ? MemberServiceTypeEnum.ALI_MEMBER : MemberServiceTypeEnum.MEMBER_CENTER;
    }

}
