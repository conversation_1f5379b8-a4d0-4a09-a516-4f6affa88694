package com.holderzone.saas.store.member.service.rpc;

import cn.hutool.core.lang.Console;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.member.entity.dto.center.ThreadLocalOperSubjectCache;
import com.holderzone.saas.store.member.utils.SignUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@Slf4j
@RequiredArgsConstructor
public class MemberCenterClientService {

    private static final int TIMEOUT = 10000; // 10 seconds
    private static final RequestConfig REQUEST_CONFIG = RequestConfig.custom().setConnectTimeout(TIMEOUT).setSocketTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT).build();

    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.custom().setDefaultRequestConfig(REQUEST_CONFIG).build();

    @Value("${member.api.base-url:http://localhost:8080/api}")
    private String memberApiBaseUrl;

    @Value("${member.api.app-id:holder-repast}")
    private String appId;

    @Value("${member.api.secret-key:aKeCec0knwJGzdQeEFSMGbtTB0dJEQDa}")
    private String secretKey;

    private static final String REPAST_SYSTEM = "1";


    /**
     * 执行会员系统请求，返回原始响应体
     *
     * @param apiPath     接口路径
     * @param requestBody 请求参数
     * @return 原始响应体字符串
     */
    public <T> String executeMemberRequestRaw(String apiPath, T requestBody) {
        return executeMemberRequest(apiPath, requestBody, data -> {
            log.info("executeMemberRequestRaw类型原始响应体: {}", data);
            if (data == null) {
                return null;
            }
            return data.toString();
        });
    }

    /**
     * 执行会员系统请求
     *
     * @param apiPath      接口路径
     * @param requestBody  请求参数
     * @param responseType 返回对象类型
     * @return 处理后的结果
     */
    public <T, R> R executeMemberRequest(String apiPath, T requestBody, Class<R> responseType) {
        return executeMemberRequest(apiPath, requestBody, data -> {
            if (data == null) {
                return null;
            }
            return JSON.parseObject(data.toString(), responseType);
        });
    }

    /**
     * 执行会员系统请求
     *
     * @param apiPath     接口路径
     * @param requestBody 请求参数
     * @param elementType 数组元素类型
     * @return 处理后的结果列表
     */
    public <T, R> List<R> executeMemberRequestList(String apiPath, T requestBody, Class<R> elementType) {
        return executeMemberRequest(apiPath, requestBody, data -> {
            if (data == null) {
                return Collections.emptyList();
            }
            return JSON.parseArray(data.toString(), elementType);
        });
    }

    /**
     * 执行会员系统请求
     *
     * @param apiPath     接口路径
     * @param requestBody 请求参数
     * @return 是否执行成功
     */
    public <T> Boolean executeMemberRequestBoolean(String apiPath, T requestBody) {
        return executeMemberRequest(apiPath, requestBody, data -> (Boolean) data);
    }

    /**
     * 执行会员系统请求
     *
     * @param apiPath     接口路径
     * @param requestBody 请求参数
     * @param processor   响应处理器
     * @return 处理后的结果
     */
    private <T, R> R executeMemberRequest(String apiPath, T requestBody, Function<Object, R> processor) {
        String url = memberApiBaseUrl + apiPath;
        try {
            long startTime = System.currentTimeMillis();
            HttpPost httpPost = new HttpPost(url);

            // Add headers
            String[] headers = buildHeaders();
            for (int i = 0; i < headers.length; i += 2) {
                httpPost.setHeader(headers[i], headers[i + 1]);
            }

            // Add request body
            if (requestBody != null) {
                StringEntity entity = new StringEntity(JSON.toJSONString(requestBody), StandardCharsets.UTF_8);
                entity.setContentType(ContentType.JSON.toString());
                httpPost.setEntity(entity);
            }

            CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            long costTime = System.currentTimeMillis() - startTime;

            logRequestTrace(url, headers, requestBody, costTime, response, responseBody);
            return processResponse(response.getStatusLine().getStatusCode(), responseBody, processor);
        } catch (Exception e) {
            log.error("请求会员异常！", e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 构建HTTP请求
     */
    private String[] buildHeaders() {
        String currentTime = String.valueOf(System.currentTimeMillis() / 1000);
        return new String[]{"Content-Type", ContentType.JSON.toString(), "App-Id", appId, "Timestamp", currentTime, "Signature", SignUtils.generateSignature(appId, secretKey, currentTime), "System", REPAST_SYSTEM, "Source", String.valueOf(ThreadLocalOperSubjectCache.getSource()), "EnterpriseGuid", String.valueOf(ThreadLocalOperSubjectCache.getCenterEnterpriseGuid()), "OperSubjectGuid", String.valueOf(ThreadLocalOperSubjectCache.getCenterOperSubjectGuid())};
    }

    /**
     * 处理响应结果
     */
    private <R> R processResponse(int statusCode, String responseBody, Function<Object, R> processor) {
        log.info("原始响应体: {}", responseBody);
        JSONObject responseJson = JSON.parseObject(responseBody);
        if (responseJson.getIntValue("code") != HttpStatus.HTTP_OK) {
            throw new BusinessException(responseJson.getString("message"));
        }

        Object data = responseJson.get("data");
        return processor.apply(data);
    }

    /**
     * 结构化日志记录 - 请求响应
     */
    private void logRequestTrace(String url, String[] headers, Object requestBody, long costMs, CloseableHttpResponse response, String responseBody) {
        log.info("\n========================================== Start ==========================================");
        log.info("URL            : " + url);
        log.info("Cost           : " + costMs);
        log.info("Status         : " + response.getStatusLine().getStatusCode());
        log.info("Request Headers   : " + abbreviate(JSON.toJSONString(headers)));
        log.info("Request Args   : " + abbreviate(JSON.toJSONString(requestBody)));
        log.info("Response Args  : " + abbreviate(responseBody));
        log.info("=========================================== End ===========================================");
    }

    /**
     * 截断过长的字符串
     */
    private String abbreviate(String str) {
        if (str == null) {
            return "";
        }
        if (str.length() <= 2000) {
            return str;
        }
        return str.substring(0, 2000) + "...";
    }

    /**
     * 执行会员系统GET请求，返回原始响应体
     *
     * @param apiPath 接口路径
     * @param params  请求参数
     * @return 原始响应体字符串
     */
    public <T> String executeMemberGetRequestRaw(String apiPath, T params) {
        return executeMemberGetRequest(apiPath, params, data -> {
            if (data == null) {
                return null;
            }
            return data.toString();
        });
    }

    /**
     * 执行会员系统GET请求
     *
     * @param apiPath      接口路径
     * @param params       请求参数
     * @param responseType 返回对象类型
     * @return 处理后的结果
     */
    public <T, R> R executeMemberGetRequest(String apiPath, T params, Class<R> responseType) {
        return executeMemberGetRequest(apiPath, params, data -> {
            if (data == null) {
                return null;
            }
            return JSON.parseObject(data.toString(), responseType);
        });
    }

    /**
     * 执行会员系统GET请求
     *
     * @param apiPath     接口路径
     * @param params      请求参数
     * @param elementType 数组元素类型
     * @return 处理后的结果列表
     */
    public <T, R> List<R> executeMemberGetRequestList(String apiPath, T params, Class<R> elementType) {
        return executeMemberGetRequest(apiPath, params, data -> {
            if (data == null) {
                return Collections.emptyList();
            }
            return JSON.parseArray(data.toString(), elementType);
        });
    }

    /**
     * 执行会员系统GET请求
     *
     * @param apiPath   接口路径
     * @param params    请求参数
     * @param processor 响应处理器
     * @return 处理后的结果
     */
    private <T, R> R executeMemberGetRequest(String apiPath, T params, Function<Object, R> processor) {
        String url = buildGetUrl(memberApiBaseUrl + apiPath, params);
        try {
            long startTime = System.currentTimeMillis();
            HttpGet httpGet = new HttpGet(url);

            // Add headers
            String[] headers = buildHeaders();
            for (int i = 0; i < headers.length; i += 2) {
                httpGet.setHeader(headers[i], headers[i + 1]);
            }

            CloseableHttpResponse response = HTTP_CLIENT.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            long costTime = System.currentTimeMillis() - startTime;

            logRequestTrace(url, headers, params, costTime, response, responseBody);
            return processResponse(response.getStatusLine().getStatusCode(), responseBody, processor);
        } catch (Exception e) {
            log.error("executeMemberGetRequest error, ", e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 构建GET请求URL
     */
    private <T> String buildGetUrl(String baseUrl, T params) {
        if (params == null) {
            return baseUrl;
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(params));
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        boolean isFirst = true;
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value != null) {
                urlBuilder.append(isFirst ? "?" : "&").append(key).append("=").append(value);
                isFirst = false;
            }
        }
        return urlBuilder.toString();
    }

}
