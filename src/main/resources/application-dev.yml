wxStorePay:
  appId: 7b44d9d3-2760-41e7-90d5-d08b93d2d693
  mchntName: 成都掌控者网络科技有限公司
  appSecret: ZPKwzfYZMiRI78/URgcioA==
test:
  hostname: test-holder-saas-register
  eureka:
    hostname: ${test.hostname}
  zipkin:
    hostname: ***************

spring:
  application:
    name: holder-saas-store-member
  zipkin:
    base-url: http://***************:9411/
    enabled: false
    sender:
      type: web
    sleuth:
      feign:
        enabled: false
      sampler:
        probability: 1.0

  rocketmq:
    namesrv-addr: test-holder-saas-rocketmq:9876
    producer-group-name: memberProduce
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120

  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null
server:
  port: 8911
    ##tomcat:
    ##accept-count: 200
    ##max-connections: 5000
  ##max-threads: 450
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得,线程,它的值设置取决于系统的负载 io-thread
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty的池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    # buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${test.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${test.eureka.hostname}:8141/eureka/
      #访问地址：**************:31141
#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true
mybatis:
  type-aliases-package: com.holderzone.saas.store.member.domain
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    #必须制定当前服务的名称
    server-code: holder_saas_store_member
#结合 mybatis
#  mybatis:
#    enabled: true
#    base-package: com.holderzone.saas.store.member.mapper
#    mapper-locations: classpath:mapper/*.xml
#    type-aliases-package: com.holderzone.saas.store.member.domain
#    type-handler-package:
agg:
  notify: http://mch-sit.holderzone.cn/gateway/member/notify
#会员中台
member:
  marketing:
    host: https://member-center-h5-sr.holderzone.cn
  api:
    base-url: https://member-sr.holderzone.cn/api
    app-id: holder-repast
    secret-key: aKeCec0knwJGzdQeEFSMGbtTB0dJEQDa